#!/usr/bin/env python3
"""
Test script for pagination enhancements in MemoryService
Tests the new pagination functionality with total count information
"""

import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock
from typing import List

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.memory_service import MemoryService
from app.models.conversation import Conversation, Message, PyObjectId
from app.models.responses import PaginatedResponse, PaginationInfo
from datetime import datetime


class MockRequest:
    """Mock request object for testing"""
    def __init__(self):
        self.state = Mock()
        self.state.user = {"member_id": 12345}


async def test_pagination_info_calculations():
    """Test PaginationInfo calculations"""
    print("Testing PaginationInfo calculations...")
    
    # Test case 1: First page with more pages available
    pagination = PaginationInfo(
        current_page=1,
        page_size=10,
        total_count=25,
        total_pages=0,  # Will be calculated
        has_next=False,  # Will be calculated
        has_previous=False  # Will be calculated
    )
    
    assert pagination.total_pages == 3, f"Expected 3 total pages, got {pagination.total_pages}"
    assert pagination.has_next == True, f"Expected has_next=True, got {pagination.has_next}"
    assert pagination.has_previous == False, f"Expected has_previous=False, got {pagination.has_previous}"
    
    # Test case 2: Middle page
    pagination = PaginationInfo(
        current_page=2,
        page_size=10,
        total_count=25,
        total_pages=0,
        has_next=False,
        has_previous=False
    )
    
    assert pagination.total_pages == 3, f"Expected 3 total pages, got {pagination.total_pages}"
    assert pagination.has_next == True, f"Expected has_next=True, got {pagination.has_next}"
    assert pagination.has_previous == True, f"Expected has_previous=True, got {pagination.has_previous}"
    
    # Test case 3: Last page
    pagination = PaginationInfo(
        current_page=3,
        page_size=10,
        total_count=25,
        total_pages=0,
        has_next=False,
        has_previous=False
    )
    
    assert pagination.total_pages == 3, f"Expected 3 total pages, got {pagination.total_pages}"
    assert pagination.has_next == False, f"Expected has_next=False, got {pagination.has_next}"
    assert pagination.has_previous == True, f"Expected has_previous=True, got {pagination.has_previous}"
    
    # Test case 4: Empty result set
    pagination = PaginationInfo(
        current_page=1,
        page_size=10,
        total_count=0,
        total_pages=0,
        has_next=False,
        has_previous=False
    )
    
    assert pagination.total_pages == 0, f"Expected 0 total pages, got {pagination.total_pages}"
    assert pagination.has_next == False, f"Expected has_next=False, got {pagination.has_next}"
    assert pagination.has_previous == False, f"Expected has_previous=False, got {pagination.has_previous}"
    
    print("✅ PaginationInfo calculations test passed!")


async def test_paginated_response_structure():
    """Test PaginatedResponse structure"""
    print("Testing PaginatedResponse structure...")
    
    # Create mock conversations
    mock_conversations = [
        Conversation(
            id=PyObjectId(),
            user_id=12345,
            title=f"Test Conversation {i}",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        ) for i in range(5)
    ]
    
    # Create pagination info
    pagination_info = PaginationInfo(
        current_page=1,
        page_size=10,
        total_count=25,
        total_pages=0,
        has_next=False,
        has_previous=False
    )
    
    # Create paginated response
    paginated_response = PaginatedResponse[Conversation](
        data=mock_conversations,
        pagination=pagination_info
    )
    
    # Verify structure
    assert len(paginated_response.data) == 5, f"Expected 5 conversations, got {len(paginated_response.data)}"
    assert paginated_response.pagination.current_page == 1, f"Expected page 1, got {paginated_response.pagination.current_page}"
    assert paginated_response.pagination.total_count == 25, f"Expected total_count 25, got {paginated_response.pagination.total_count}"
    assert paginated_response.pagination.total_pages == 3, f"Expected 3 total pages, got {paginated_response.pagination.total_pages}"
    
    print("✅ PaginatedResponse structure test passed!")


async def test_memory_service_error_handling():
    """Test MemoryService error handling for pagination"""
    print("Testing MemoryService error handling...")
    
    # Create mock request
    mock_request = MockRequest()
    
    # Create memory service instance
    memory_service = MemoryService(mock_request)
    
    # Test get_user_conversations with no connection (should return empty paginated response)
    try:
        result = await memory_service.get_user_conversations(user_id=12345, page_size=10, page=1)
        
        # Verify it returns a PaginatedResponse even on error
        assert isinstance(result, PaginatedResponse), f"Expected PaginatedResponse, got {type(result)}"
        assert len(result.data) == 0, f"Expected empty data on error, got {len(result.data)} items"
        assert result.pagination.total_count == 0, f"Expected total_count 0 on error, got {result.pagination.total_count}"
        
        print("✅ get_user_conversations error handling test passed!")
        
    except Exception as e:
        print(f"❌ get_user_conversations error handling test failed: {e}")
    
    # Test get_conversation_messages with invalid conversation_id
    try:
        result = await memory_service.get_conversation_messages(conversation_id="", page_size=10, page=1)
        
        # Verify it returns a PaginatedResponse for empty conversation_id
        assert isinstance(result, PaginatedResponse), f"Expected PaginatedResponse, got {type(result)}"
        assert len(result.data) == 0, f"Expected empty data for invalid conversation_id, got {len(result.data)} items"
        assert result.pagination.total_count == 0, f"Expected total_count 0 for invalid conversation_id, got {result.pagination.total_count}"
        
        print("✅ get_conversation_messages error handling test passed!")
        
    except Exception as e:
        print(f"❌ get_conversation_messages error handling test failed: {e}")


def test_api_response_format():
    """Test API response format transformation"""
    print("Testing API response format transformation...")
    
    # Create mock paginated response
    mock_conversations = [
        {"id": "conv1", "title": "Test 1"},
        {"id": "conv2", "title": "Test 2"}
    ]
    
    pagination_info = PaginationInfo(
        current_page=1,
        page_size=10,
        total_count=25,
        total_pages=0,
        has_next=False,
        has_previous=False
    )
    
    # Simulate the API response format transformation
    api_response = {
        "conversations": mock_conversations,
        "pagination": {
            "current_page": pagination_info.current_page,
            "page_size": pagination_info.page_size,
            "total_count": pagination_info.total_count,
            "total_pages": pagination_info.total_pages,
            "has_next": pagination_info.has_next,
            "has_previous": pagination_info.has_previous
        }
    }
    
    # Verify the structure
    assert "conversations" in api_response, "API response should have 'conversations' key"
    assert "pagination" in api_response, "API response should have 'pagination' key"
    assert api_response["pagination"]["total_count"] == 25, f"Expected total_count 25, got {api_response['pagination']['total_count']}"
    assert api_response["pagination"]["total_pages"] == 3, f"Expected total_pages 3, got {api_response['pagination']['total_pages']}"
    
    print("✅ API response format test passed!")


async def main():
    """Run all tests"""
    print("🧪 Starting pagination enhancement tests...\n")
    
    try:
        # Test pagination calculations
        await test_pagination_info_calculations()
        print()
        
        # Test paginated response structure
        await test_paginated_response_structure()
        print()
        
        # Test error handling
        await test_memory_service_error_handling()
        print()
        
        # Test API response format
        test_api_response_format()
        print()
        
        print("🎉 All pagination tests passed successfully!")
        print("\n📋 Test Summary:")
        print("✅ PaginationInfo calculations work correctly")
        print("✅ PaginatedResponse structure is valid")
        print("✅ Error handling returns proper empty responses")
        print("✅ API response format transformation works")
        print("\n🚀 Pagination enhancements are ready for production!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
