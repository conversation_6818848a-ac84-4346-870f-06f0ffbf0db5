from fastapi import APIRouter, HTTPException, Depends, Request, Body
from fastapi.responses import JSONResponse
from app.models.requests import VehicleQueryRequest, ChatHistoryListRequest, ChatHistoryRequest, FeedbackRequest, DeleteMessageRequest, MessageUpdate
from app.models.responses import VehicleQueryResponse, ResponseType
import logging
import json
import re
import time
from decimal import Decimal
from app.services.memory_service import MemoryService
from app.services.system_prompt_service import SystemPromptService
from app.config.logger import log_info, log_error
from app.utils.constant import constant
from app.utils.helper import helper
from app.utils.message import message
# Import necessary modules for the new implementation
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from langchain_community.utilities import SerpAPIWrapper
from app.config.settings import settings
from app.services.user_service import userService
# Import required services
from app.services.mc_vehicle_inventory_service import VehicleInventoryService
from app.services.proximity_service import ProximityService

router = APIRouter()


def convert_decimals_to_serializable(obj):
    """
    Convert Decimal objects to float for JSON serialization
    """
    if isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, dict):
        return {key: convert_decimals_to_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_decimals_to_serializable(item) for item in obj]
    else:
        return obj


def extract_filter_from_response(ai_response: str, request: Request) -> tuple:
    """
    Extract filter JSON from AI response and return modified message.
    Returns: (filter_data, modified_message)
    """
    try:
        log_info(request, "Checking AI response for filter JSON data")

        # Look for JSON patterns in the response
        json_pattern = r'\{[^{}]*"filters"[^{}]*\{[^{}]*\}[^{}]*\}'
        matches = re.findall(json_pattern, ai_response, re.DOTALL)

        if not matches:
            # Try broader JSON pattern
            json_pattern = r'\{[^{}]*"make"[^{}]*"model"[^{}]*\}'
            matches = re.findall(json_pattern, ai_response, re.DOTALL)

        if matches:
            for match in matches:
                try:
                    # Try to parse the JSON
                    json_data = json.loads(match)

                    # Check if it contains filter-like data
                    if "filters" in json_data:
                        filter_data = json_data["filters"]
                    elif any(key in json_data for key in ["make", "model", "year", "trim", "price_max", "price_min"]):
                        filter_data = json_data # Keep as dict, don't convert to string
                    else:
                        continue

                    # Modify the message to indicate system is searching
                    modified_message = "I found your specific vehicle requirements. Let me search our inventory for vehicles that match your criteria..."

                    log_info(request, f"Successfully extracted filter data: {filter_data}")
                    return filter_data, modified_message

                except json.JSONDecodeError:
                    continue

        # No valid filter JSON found
        log_info(request, "No filter JSON detected in AI response")
        return None, None

    except Exception as e:
        log_error(request, f"Error extracting filter from response: {str(e)}")
        return None, None


@router.post("/vehicle/query")
async def vehicle_query(request: Request, body: VehicleQueryRequest = Body(...)):
    """
    Query the vehicle expert agent
    """
    try:
        # Start overall timing
        overall_start_time = time.time()
        log_info(request, "[TIMING] VehicleQuery.vehicle_query: Starting vehicle query processing")

        # Get authenticated user information from request state
        user_id = request.state.user["member_id"]
        logged_in_user_name = body.user_name
        user_zip_code = body.zipcode
        # user current budget against which vehicle details will be shwon
        user_amount = body.user_purchase_power_amount
        # loan amount aprroved for user to be used.
        approved_amount = body.user_loan_approval_amount
        # Received any filter
        payload_filter = body.filters
        log_info(request, f"Processing query for member_id: {user_id}")
        if payload_filter:
            log_info(request, f"Received filter data in payload: {payload_filter}, Hence initiing the fitler search against vehicle query")

            # Time proximity service operations
            proximity_start_time = time.time()
            log_info(request, "[TIMING] ProximityService.process: Starting dealer proximity processing")

            # Get nearby dealers with initial radius
            response = VehicleQueryResponse()
            proximity_service = ProximityService(request)
            person_location = {"lat": body.lat, "lon": body.long}

            dealers = await proximity_service.read_dealers_from_file(proximity_service.dealers_path)

            nearby_dealers = await proximity_service.find_nearby_dealers(
                person_location, dealers, proximity_service.max_user_distance
            )

            dealer_ids = [int(dealer["dealer_id"]) for dealer in nearby_dealers]
            proximity_elapsed = time.time() - proximity_start_time
            log_info(request, f"[TIMING] ProximityService.process: Dealer proximity processing completed - Elapsed: {proximity_elapsed:.3f}s")

            # Time inventory service operations
            inventory_start_time = time.time()
            log_info(request, "[TIMING] VehicleInventoryService.get_filtered_inventory: Starting inventory database query")

            # Get inventory using the filter data with initial radius
            inventory_service = VehicleInventoryService(request)
            await inventory_service.connect()
            try:
                inventory_result = await inventory_service.get_filtered_inventory(
                    filters=payload_filter,
                    approved_amount=approved_amount,
                    req=request,
                    page=body.page,
                    page_size=body.page_size,
                    sort_by=body.sort_by,
                    sort_order=body.sort_order,
                    dealer_ids=dealer_ids
                )
                inventory_vehicles = inventory_result.get("vehicles", [])  # Extract vehicles from the new format
                total_vehicle_count = inventory_result.get("total_vehicle_count", 0)  # Extract total count
            finally:
                await inventory_service.close()
                inventory_elapsed = time.time() - inventory_start_time
                log_info(request, f"[TIMING] VehicleInventoryService.get_filtered_inventory: Inventory database query completed - Elapsed: {inventory_elapsed:.3f}s")

            # Time response preparation
            response_prep_start_time = time.time()
            response.vehicles = inventory_vehicles
            response.total_vehicle_count = 0 if total_vehicle_count is None else total_vehicle_count   # Set total count in response
            response_prep_elapsed = time.time() - response_prep_start_time
            log_info(request, f"[TIMING] VehicleQuery.response_preparation: Filter-based response preparation completed - Elapsed: {response_prep_elapsed:.3f}s")

            overall_elapsed = time.time() - overall_start_time
            log_info(request, f"[TIMING] VehicleQuery.vehicle_query: Filter-based vehicle query processing completed - Total Elapsed: {overall_elapsed:.3f}s")
            return helper.return_response(constant.SUCCESS, response, message.fetched_vehicle_agent_success)
        # Extract user information for personalization
        user_name = logged_in_user_name
        user_budget = user_amount
        user_location = user_zip_code

        # Time memory service operations
        memory_start_time = time.time()
        log_info(request, "[TIMING] MemoryService.connect: Starting memory service connection")

        # Initialize memory service for conversation management
        memory_service = MemoryService(request)
        await memory_service.connect()
        memory_connect_elapsed = time.time() - memory_start_time
        log_info(request, f"[TIMING] MemoryService.connect: Memory service connection completed - Elapsed: {memory_connect_elapsed:.3f}s")

        # Time conversation management
        conversation_start_time = time.time()
        log_info(request, "[TIMING] MemoryService.conversation_management: Starting conversation retrieval/creation")

        # Handle conversation_id - create new or use existing
        conversation_id = body.conversation_id
        if not conversation_id:
            conversation = await memory_service.create_conversation(user_id=user_id, title=body.query[:50])
            conversation_id = str(conversation.id)
            log_info(request, f"New conversation created with ID: {conversation_id}")
        else:
            conversation = await memory_service.get_conversation(conversation_id)
            if not conversation:
                conversation = await memory_service.create_conversation(user_id=user_id, title=body.query[:50])
                conversation_id = str(conversation.id)
                log_info(request, f"Invalid conversation ID provided. Created new conversation: {conversation_id}")

        # Get past conversation context (last 10 messages for context)
        messages_result = await memory_service.get_conversation_messages(conversation_id, page_size=10, page=1)
        past_messages = messages_result.get("messages", [])  # Extract messages from the new format
        conversation_elapsed = time.time() - conversation_start_time
        log_info(request, f"[TIMING] MemoryService.conversation_management: Conversation management completed - Elapsed: {conversation_elapsed:.3f}s")

        # Time SerpAPI tool initialization
        serp_start_time = time.time()
        log_info(request, "[TIMING] SerpAPIWrapper.init: Starting SerpAPI tool initialization")

        # Initialize SerpAPI tool
        serp = SerpAPIWrapper()

        def websearch_tool(query: str) -> str:
            """Real-time web search using SerpAPI."""
            return serp.run(query)

        serp_elapsed = time.time() - serp_start_time
        log_info(request, f"[TIMING] SerpAPIWrapper.init: SerpAPI tool initialization completed - Elapsed: {serp_elapsed:.3f}s")

        # Time system prompt service operations
        prompt_start_time = time.time()
        log_info(request, "[TIMING] SystemPromptService.get_current_prompt: Starting system prompt retrieval")

        # Fetch system prompt from MongoDB
        prompt_service = SystemPromptService(request)
        await prompt_service.connect()
        system_prompt = await prompt_service.get_current_prompt()
        await prompt_service.close()
        prompt_elapsed = time.time() - prompt_start_time
        log_info(request, f"[TIMING] SystemPromptService.get_current_prompt: System prompt retrieval completed - Elapsed: {prompt_elapsed:.3f}s")

        # Time agent creation
        agent_start_time = time.time()
        log_info(request, "[TIMING] LangGraph.create_react_agent: Starting agent creation")

        # Initialize checkpointer with dynamic thread_id based on conversation
        checkpointer = MemorySaver()
        config = {"configurable": {"thread_id": conversation_id}}

        # Create the agent with the updated prompt using settings model
        agent = create_react_agent(
            model=f"openai:{settings.OPENAI_MODEL}",
            tools=[websearch_tool],
            checkpointer=checkpointer,
            prompt=system_prompt
        )
        agent_elapsed = time.time() - agent_start_time
        log_info(request, f"[TIMING] LangGraph.create_react_agent: Agent creation completed - Elapsed: {agent_elapsed:.3f}s")

        # Time message preparation
        message_prep_start_time = time.time()
        log_info(request, "[TIMING] VehicleQuery.message_preparation: Starting message context preparation")

        # Prepare user context as knowledge base
        user_context_knowledge = f"""
            USER CONTEXT KNOWLEDGE BASE:
            - Name: {user_name}
            - Budget: ${user_budget:,.2f}
            - Location: {user_location}
            - User ID: {user_id}

            Use this information to personalize recommendations and filter vehicles within the budget and suitable for the location.
            """

        # Prepare messages with context
        messages = []

        # Add user context as system knowledge
        messages.append({"role": "system", "content": user_context_knowledge})

        # Add past conversation context if available
        if past_messages:
            log_info(request, f"Adding {len(past_messages)} past messages for context")
            for msg in reversed(past_messages):  # Reverse to get chronological order
                messages.append({"role": msg.role, "content": msg.content})

        # Add current user query
        user_query = body.query if body.query else "i want to continue with this"
        messages.append({"role": "user", "content": user_query})
        message_prep_elapsed = time.time() - message_prep_start_time
        log_info(request, f"[TIMING] VehicleQuery.message_preparation: Message context preparation completed - Elapsed: {message_prep_elapsed:.3f}s")

        # Time agent invocation (critical operation)
        agent_invoke_start_time = time.time()
        log_info(request, "[TIMING] LangGraph.agent_invoke: Starting AI agent invocation")

        # Invoke the agent
        result = agent.invoke({"messages": messages}, config)
        agent_invoke_elapsed = time.time() - agent_invoke_start_time
        log_info(request, f"[TIMING] LangGraph.agent_invoke: AI agent invocation completed - Elapsed: {agent_invoke_elapsed:.3f}s")

        # Get AI response content
        ai_response = result["messages"][-1].content

        # Time filter extraction
        filter_extract_start_time = time.time()
        log_info(request, "[TIMING] VehicleQuery.filter_extraction: Starting filter data extraction from AI response")

        # Check if AI response contains filter JSON and extract it
        filter_data, _ = extract_filter_from_response(ai_response, request)
        filter_extract_elapsed = time.time() - filter_extract_start_time
        log_info(request, f"[TIMING] VehicleQuery.filter_extraction: Filter data extraction completed - Elapsed: {filter_extract_elapsed:.3f}s")

        # Time memory service message saving
        memory_save_start_time = time.time()
        log_info(request, "[TIMING] MemoryService.add_message: Starting user message save")

        # Save the user message first
        await memory_service.add_message(conversation_id=conversation_id, user_id=user_id, content=user_query, role="user")
        memory_save_elapsed = time.time() - memory_save_start_time
        log_info(request, f"[TIMING] MemoryService.add_message: User message save completed - Elapsed: {memory_save_elapsed:.3f}s")

        # Create response object
        response = VehicleQueryResponse()
        response.vehicles = []
        response.message = ai_response
        response.response_type = ResponseType.INFORMATION
        response.success = True
        response.filters = filter_data
        response.metadata = {
            "conversation_id": conversation_id,
            "user_context": {
                "name": user_name,
                "budget": user_budget,
                "location": user_location
            }
        }
        # If filter data was detected, fetch inventory
        if filter_data:
            # Time conditional proximity service operations
            conditional_proximity_start_time = time.time()
            log_info(request, "[TIMING] ProximityService.conditional_process: Starting conditional dealer proximity processing")

            # Get nearby dealers with initial radius
            proximity_service = ProximityService(request)
            person_location = {"lat": body.lat, "lon": body.long}

            dealers = await proximity_service.read_dealers_from_file(proximity_service.dealers_path)

            nearby_dealers = await proximity_service.find_nearby_dealers(
                person_location, dealers, proximity_service.max_user_distance
            )

            dealer_ids = [int(dealer["dealer_id"]) for dealer in nearby_dealers]
            conditional_proximity_elapsed = time.time() - conditional_proximity_start_time
            log_info(request, f"[TIMING] ProximityService.conditional_process: Conditional dealer proximity processing completed - Elapsed: {conditional_proximity_elapsed:.3f}s")

            # Time conditional inventory service operations
            conditional_inventory_start_time = time.time()
            log_info(request, "[TIMING] VehicleInventoryService.conditional_query: Starting conditional inventory database query")

            # Get inventory using the filter data with initial radius
            inventory_service = VehicleInventoryService(request)
            await inventory_service.connect()
            try:
                inventory_result = await inventory_service.get_filtered_inventory(
                    filters=filter_data,
                    approved_amount=approved_amount,
                    req=request,
                    page=body.page,
                    page_size=body.page_size,
                    sort_by=body.sort_by,
                    sort_order=body.sort_order,
                    dealer_ids=dealer_ids
                )
                inventory_vehicles = inventory_result.get("vehicles", [])  # Extract vehicles from the new format
                total_vehicle_count = inventory_result.get("total_vehicle_count", 0)  # Extract total count
            finally:
                await inventory_service.close()
                conditional_inventory_elapsed = time.time() - conditional_inventory_start_time
                log_info(request, f"[TIMING] VehicleInventoryService.conditional_query: Conditional inventory database query completed - Elapsed: {conditional_inventory_elapsed:.3f}s")

            # Add inventory data to response
            response.vehicles = inventory_vehicles
            response.total_vehicle_count = 0 if total_vehicle_count is None else total_vehicle_count # Set total count in response
            response.metadata["inventory_vehicles"] = len(inventory_vehicles)
            response.metadata["is_inventory_fetch"] = 1

            log_info(request, f"Successfully processed inventory search with {len(inventory_vehicles)} vehicles")

            # Time data serialization and memory save operations
            data_processing_start_time = time.time()
            log_info(request, "[TIMING] VehicleQuery.data_serialization: Starting vehicle data serialization and memory save")

            # Save AI message with inventory data when vehicles are found
            if len(inventory_vehicles) > 0:
                # Convert vehicle data to serializable format
                serializable_vehicles = []
                for vehicle in inventory_vehicles:
                    if hasattr(vehicle, 'model_dump'):
                        vehicle_data = vehicle.model_dump()
                    else:
                        vehicle_data = vehicle
                    # Convert Decimal objects to float
                    serializable_vehicle = convert_decimals_to_serializable(vehicle_data)
                    serializable_vehicles.append(serializable_vehicle)
                fetched_message = await memory_service.add_message(
                    conversation_id=conversation_id,
                    user_id=user_id,
                    content=response.message,
                    role="ai",
                    inventory_vehicles=serializable_vehicles,
                    is_inventory_fetch=1
                )
            else:
                # Save AI message without inventory data when no vehicles found
                fetched_message = await memory_service.add_message(
                    conversation_id=conversation_id,
                    user_id=user_id,
                    content=response.message,
                    role="ai",
                    is_inventory_fetch=1
                )
            data_processing_elapsed = time.time() - data_processing_start_time
            log_info(request, f"[TIMING] VehicleQuery.data_serialization: Vehicle data serialization and memory save completed - Elapsed: {data_processing_elapsed:.3f}s")

            # except Exception as inventory_error:
            #     log_error(request, f"Error fetching inventory: {str(inventory_error)}")
            #     # Save AI message without inventory data on error
            #     await memory_service.add_message(
            #         conversation_id=conversation_id,
            #         user_id=str(user_id),
            #         content=ai_response,
            #         role="ai"
            #     )
        else:
            # Time normal AI response save
            normal_save_start_time = time.time()
            log_info(request, "[TIMING] MemoryService.normal_save: Starting normal AI response save")

            # No filter data detected, save normal AI response
            fetched_message = await memory_service.add_message(
                conversation_id=conversation_id,
                user_id=user_id,
                content=ai_response,
                role="ai"
            )
            normal_save_elapsed = time.time() - normal_save_start_time
            log_info(request, f"[TIMING] MemoryService.normal_save: Normal AI response save completed - Elapsed: {normal_save_elapsed:.3f}s")

        # Time memory service cleanup
        cleanup_start_time = time.time()
        log_info(request, "[TIMING] MemoryService.close: Starting memory service cleanup")

        # Close memory service connection
        await memory_service.close()
        cleanup_elapsed = time.time() - cleanup_start_time
        log_info(request, f"[TIMING] MemoryService.close: Memory service cleanup completed - Elapsed: {cleanup_elapsed:.3f}s")

        response.metadata["message_id"] = str(fetched_message.id)

        # Log overall completion timing
        overall_elapsed = time.time() - overall_start_time
        log_info(request, f"[TIMING] VehicleQuery.vehicle_query: Complete vehicle query processing finished - Total Elapsed: {overall_elapsed:.3f}s")
        log_info(request, "Query processed successfully with dynamic user context and conversation management")
        return helper.return_response(constant.SUCCESS, response, message.fetched_vehicle_agent_success)

    except Exception as e:
        log_error(request, f"Error processing query: {str(e)}")
        return helper.return_error(constant.FAIL, message.fetched_vehicle_agent_error)


@router.post("/vehicle/fetch-chat-history-list")
async def chat_history_list(request: Request, body: ChatHistoryListRequest = Body(...)):
    """
    Fetch the chat history list for a user, with optional search on conversation title.
    """
    try:
        log_info(request, "Initiated to fetch chat list history...")

        memory_service = MemoryService(request)

        await memory_service.connect()

        chat_history_list = []
        user_id = request.state.user["member_id"]
        # user = userService(request)
        # user_details = user.get_user_loggedin_user_details(request.state.user["member_id"])
        # user_id = user_details['user_id']
        if body.search:
            # Search logic with pagination
            chat_history_list = await memory_service.get_user_search_conversation(body.search, user_id, body.page_size, body.page)
        else:
            # Paginated history fetch
            chat_history_list = await memory_service.get_user_conversations(user_id, body.page_size, body.page)

        await memory_service.close()

        log_info(request, "Fetched chat list history successfully.")
        return helper.return_response(constant.SUCCESS, chat_history_list, message.fetch_chat_history_list_success)

    except Exception as e:
        log_error(request, f"Error while fetching chat history list: {str(e)}")
        return helper.return_error(constant.FAIL, message.fetch_chat_history_list_error)

@router.post("/vehicle/fetch-chat-history")
async def chat_history_list(request: Request, body: ChatHistoryRequest = Body(...)):
    """
    Fetch the chat history list against the user
    """
    try:
        log_info(request, "Initiated search for user conversation history...")

        memory_service = MemoryService(request)
        await memory_service.connect()

        chat_history_list = await memory_service.get_conversation_messages(
            body.conversation_id, body.page_size, body.page
        )

        await memory_service.close()

        log_info(request, "Completed search for user conversation history")

        return helper.return_response(
            constant.SUCCESS,
            chat_history_list,
            message.fetch_user_conversation_success
        )

    except Exception as e:
        log_error(request, f"Error while fetching user conversation: {str(e)}")
        return helper.return_error(
            constant.FAIL,
            message.fetch_user_conversation_error
        )


@router.post("/vehicle/feedback")
async def feedback_message(request: Request, body: FeedbackRequest = Body(...)):
    """
    Feedback message against the agent response
    """
    try:
        if not body.message_id:
            log_info(request, "No message ID provided.")
            return helper.return_error(constant.FAIL, message.feedback_provided_error)
        if body.feedback_message and body.feedback_flag == 1:
            log_info(request, f"Received feedback message against feedback_flag 1: {body.feedback_message}")
            return helper.return_error(constant.FAIL, message.feedback_provided_error)
        log_info(request, f"Received feedback for message ID: {body.message_id}")
        memory_service = MemoryService(request)
        await memory_service.connect()

        try:
            updated_message = await memory_service.get_feedback_agent_response(
                body.message_id, body.feedback_flag, body.feedback_message
            )
            if updated_message is None:
                log_info(request, "Trouble updating the feedback.")
                return helper.return_error(constant.FAIL, message.feedback_provided_error)

            return helper.return_response(constant.SUCCESS, updated_message, message.feedback_provided_successfully)
        finally:
            await memory_service.close()

    except Exception as e:
        log_error(request, f"Error providing feedback: {str(e)}")
        return helper.return_error(constant.FAIL, message.feedback_provided_error)


@router.post("/vehicle/message/delete")
async def delete_message(request: Request, body: DeleteMessageRequest = Body(...)):
    """
    Delete a message from a conversation
    """
    try:
        log_info(request, f"Received request to delete message ID: {body.conversation_id}")

        memory_service = MemoryService(request)
        await memory_service.connect()

        deleted = await memory_service.soft_delete_conversation_async(body.conversation_id)

        await memory_service.close()

        if deleted:
            log_info(request, f"Successfully deleted conversation ID: {body.conversation_id}")
            return helper.return_response(constant.SUCCESS, None, message.coversation_deleted_successfully)
        else:
            log_error(request, f"Failed to delete message ID: {body.conversation_id}")
            return helper.return_error(constant.FAIL, message.coversation_deleted_error)

    except Exception as e:
        log_error(request, f"Error deleting message: {str(e)}")
        return helper.return_error(constant.FAIL, message.coversation_deleted_error)

@router.post("/vehicle/message/update")
async def update_message(request: Request, body: MessageUpdate = Body(...)):
    """
    Update a message in a conversation
    """
    try:
        log_info(request, f"Received request to update message ID: {body.message_id}")

        memory_service = MemoryService(request)
        await memory_service.connect()

        updated_message = await memory_service.update_message(
            body.message_id, body.content, body.is_inventory_fetch, body.inventory_vehicles
        )

        await memory_service.close()

        if updated_message:
            log_info(request, f"Message {body.message_id} updated successfully")
            return helper.return_response(constant.SUCCESS, None, message.message_update_success)
        else:
            log_error(request, f"Message with ID {body.message_id} not found")
            return helper.return_error(constant.FAIL, message.message_updated_failed)

    except Exception as e:
        log_error(request, f"Error updating message: {str(e)}")
        return helper.return_error(constant.FAIL, message.message_update_error)
